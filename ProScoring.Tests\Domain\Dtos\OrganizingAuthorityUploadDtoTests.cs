using System;
using FluentAssertions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain.Dtos
{
    public class OrganizingAuthorityUploadDtoTests
    {
        // --- Tests for ToEntity() method ---

        [Fact]
        public void ToEntity_AllPropertiesSet_MapsCorrectlyToOrganizingAuthority()
        {
            // Arrange
            var dto = new OrganizingAuthorityUploadDto
            {
                Id = "oa-123",
                Name = "Test OA Name",
                Email = "<EMAIL>",
                Phone = "************",
                Website = "http://testoa.com",
                Private = true,
                AddressLine1 = "123 Main St",
                AddressLine2 = "Suite 100",
                City = "Test City",
                State = "TS",
                PostalCode = "12345",
                Country = "TCY",
                Approved = true,
                ImageId = "img-456"
            };

            // Act
            var entity = dto.ToEntity();

            // Assert
            entity.Should().NotBeNull();
            entity.Id.Should().Be(dto.Id);
            entity.Name.Should().Be(dto.Name);
            entity.Email.Should().Be(dto.Email);
            entity.Phone.Should().Be(dto.Phone);
            entity.Website.Should().Be(dto.Website);
            entity.Private.Should().Be(dto.Private);
            entity.AddressLine1.Should().Be(dto.AddressLine1);
            entity.AddressLine2.Should().Be(dto.AddressLine2);
            entity.City.Should().Be(dto.City);
            entity.State.Should().Be(dto.State);
            entity.PostalCode.Should().Be(dto.PostalCode);
            entity.Country.Should().Be(dto.Country);
            entity.Approved.Should().Be(dto.Approved);
            entity.ImageId.Should().Be(dto.ImageId);
        }

        [Fact]
        public void ToEntity_IdIsNull_MapsIdAsNullInEntity()
        {
            // Arrange
            var dto = new OrganizingAuthorityUploadDto
            {
                Id = null,
                Name = "Test OA Name",
                Email = "<EMAIL>",
                Approved = false // Other properties can be minimal for this test
            };

            // Act
            var entity = dto.ToEntity();

            // Assert
            entity.Should().NotBeNull();
            entity.Id.Should().BeNull();
            entity.Name.Should().Be(dto.Name);
            entity.Email.Should().Be(dto.Email);
            entity.Approved.Should().Be(dto.Approved);
        }

        [Fact]
        public void ToEntity_SomePropertiesNull_MapsNullsCorrectly()
        {
            // Arrange
            var dto = new OrganizingAuthorityUploadDto
            {
                Id = "oa-789",
                Name = "Another OA",
                Email = "<EMAIL>",
                Phone = null, // Nullable string
                Website = null, // Nullable string
                Private = false,
                AddressLine1 = "456 Side St",
                AddressLine2 = null, // Nullable string
                City = "Otherville",
                State = "OS",
                PostalCode = "67890",
                Country = "OCY",
                Approved = true,
                ImageId = null // Nullable string
            };

            // Act
            var entity = dto.ToEntity();

            // Assert
            entity.Should().NotBeNull();
            entity.Id.Should().Be(dto.Id);
            entity.Name.Should().Be(dto.Name);
            entity.Email.Should().Be(dto.Email);
            entity.Phone.Should().BeNull();
            entity.Website.Should().BeNull();
            entity.Private.Should().Be(dto.Private);
            entity.AddressLine1.Should().Be(dto.AddressLine1);
            entity.AddressLine2.Should().BeNull();
            entity.City.Should().Be(dto.City);
            entity.State.Should().Be(dto.State);
            entity.PostalCode.Should().Be(dto.PostalCode);
            entity.Country.Should().Be(dto.Country);
            entity.Approved.Should().Be(dto.Approved);
            entity.ImageId.Should().BeNull();
        }

        // --- Test for ToEntity(string imageId) overload ---

        [Fact]
        public void ToEntityWithImageId_OverridesDtoImageId_MapsCorrectly()
        {
            // Arrange
            var dto = new OrganizingAuthorityUploadDto
            {
                Id = "oa-abc",
                Name = "Overridden Image OA",
                Email = "<EMAIL>",
                ImageId = "dto_image_id" // DTO's ImageId
                // Other properties can be set or default
            };
            var overrideImageId = "override_image_id";

            // Act
            var entity = dto.ToEntity(overrideImageId);

            // Assert
            entity.Should().NotBeNull();
            entity.Id.Should().Be(dto.Id);
            entity.Name.Should().Be(dto.Name);
            entity.Email.Should().Be(dto.Email);
            entity.ImageId.Should().Be(overrideImageId); // Crucial assertion
        }

        // --- Tests for static FromEntity(OrganizingAuthority entity) method ---

        [Fact]
        public void FromEntity_AllPropertiesSet_MapsCorrectlyToDto()
        {
            // Arrange
            var entity = new OrganizingAuthority
            {
                Id = "ent-123",
                Name = "Entity Name",
                Email = "<EMAIL>",
                Phone = "************",
                Website = "http://entity.org",
                Private = false,
                AddressLine1 = "789 Entity Ave",
                AddressLine2 = "Apt B",
                City = "Entityville",
                State = "ES",
                PostalCode = "54321",
                Country = "ENT",
                Approved = false,
                ImageId = "img-ent-789",
                // Properties not in DTO like CreatedDate, UpdatedDate, etc., are ignored
                CreatedDate = DateTimeOffset.UtcNow,
                UpdatedDate = DateTimeOffset.UtcNow
            };

            // Act
            var dto = OrganizingAuthorityUploadDto.FromEntity(entity);

            // Assert
            dto.Should().NotBeNull();
            dto.Id.Should().Be(entity.Id);
            dto.Name.Should().Be(entity.Name);
            dto.Email.Should().Be(entity.Email);
            dto.Phone.Should().Be(entity.Phone);
            dto.Website.Should().Be(entity.Website);
            dto.Private.Should().Be(entity.Private);
            dto.AddressLine1.Should().Be(entity.AddressLine1);
            dto.AddressLine2.Should().Be(entity.AddressLine2);
            dto.City.Should().Be(entity.City);
            dto.State.Should().Be(entity.State);
            dto.PostalCode.Should().Be(entity.PostalCode);
            dto.Country.Should().Be(entity.Country);
            dto.Approved.Should().Be(entity.Approved);
            dto.ImageId.Should().Be(entity.ImageId);
        }

        [Fact]
        public void FromEntity_SomePropertiesNull_MapsNullsCorrectlyToDto()
        {
            // Arrange
            var entity = new OrganizingAuthority
            {
                Id = "ent-456",
                Name = "Entity With Nulls",
                Email = "<EMAIL>",
                Phone = null,
                Website = null,
                Private = true,
                AddressLine1 = "Main St Only",
                AddressLine2 = null,
                City = "Nullville",
                State = "NS",
                PostalCode = "00000",
                Country = "NUL",
                Approved = true,
                ImageId = null
            };

            // Act
            var dto = OrganizingAuthorityUploadDto.FromEntity(entity);

            // Assert
            dto.Should().NotBeNull();
            dto.Id.Should().Be(entity.Id);
            dto.Name.Should().Be(entity.Name);
            dto.Email.Should().Be(entity.Email);
            dto.Phone.Should().BeNull();
            dto.Website.Should().BeNull();
            dto.Private.Should().Be(entity.Private);
            dto.AddressLine1.Should().Be(entity.AddressLine1);
            dto.AddressLine2.Should().BeNull();
            dto.City.Should().Be(entity.City);
            dto.State.Should().Be(entity.State);
            dto.PostalCode.Should().Be(entity.PostalCode);
            dto.Country.Should().Be(entity.Country);
            dto.Approved.Should().Be(entity.Approved);
            dto.ImageId.Should().BeNull();
        }
    }
}
