using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using Xunit;
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using ProScoring.Infrastructure.Exceptions; // For EmailException
using ProScoring.Domain.Common; // For ConfigurationErrorsException
using System.Linq; // For .AsEnumerable()
using System.Collections.Generic; // For List

namespace ProScoring.Tests.Infrastructure.Services
{
    public class PostmarkEmailSenderServiceTests
    {
        private readonly IConfiguration _configurationSubstitute; // Changed
        private readonly ILogger<PostmarkEmailSenderService> _loggerSubstitute; // Changed
        private PostmarkEmailSenderService _postmarkEmailSenderService;

        public PostmarkEmailSenderServiceTests()
        {
            _configurationSubstitute = Substitute.For<IConfiguration>();
            _loggerSubstitute = Substitute.For<ILogger<PostmarkEmailSenderService>>();
        }

        // Helper to create a substitute for IConfigurationSection representing a specific string value
        private IConfigurationSection CreateConfigSectionSubstitute(string key, string value, string parentPath = EmailSenderOptions.SECTION_NAME)
        {
            var section = Substitute.For<IConfigurationSection>();
            section.Key.Returns(key);
            section.Path.Returns($"{parentPath}:{key}");
            section.Value.Returns(value);
            section.Exists().Returns(true);
            return section;
        }

        // Helper to create a substitute for IConfigurationSection that "does not exist"
        private IConfigurationSection CreateNonExistentConfigSectionSubstitute(string key, string parentPath = EmailSenderOptions.SECTION_NAME)
        {
            var section = Substitute.For<IConfigurationSection>();
            section.Key.Returns(key);
            section.Path.Returns($"{parentPath}:{key}");
            section.Value.Returns((string)null);
            section.Exists().Returns(false); // Key does not exist
            return section;
        }


        private void SetupEmailOptions(bool enabled, string apiKey, string fromAddress, string fromName = "Test App")
        {
            var sectionSubstitute = Substitute.For<IConfigurationSection>();
            sectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Exists().Returns(true);
            sectionSubstitute.Value.Returns((string)null);

            var childrenSubstitutes = new List<IConfigurationSection>();

            var enabledSubstituteSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.Enabled), enabled.ToString().ToLowerInvariant());
            childrenSubstitutes.Add(enabledSubstituteSection);

            IConfigurationSection apiKeySubstituteSection;
            if (apiKey != null)
            {
                apiKeySubstituteSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey), apiKey);
            }
            else // Simulate key is missing
            {
                apiKeySubstituteSection = CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey));
            }
            childrenSubstitutes.Add(apiKeySubstituteSection);


            IConfigurationSection fromAddressSubstituteSection;
            if (fromAddress != null)
            {
                fromAddressSubstituteSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromAddress), fromAddress);
            }
            else // Simulate key is missing
            {
                fromAddressSubstituteSection = CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.FromAddress));
            }
            childrenSubstitutes.Add(fromAddressSubstituteSection);

            var fromNameSubstituteSection = CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromName), fromName);
            childrenSubstitutes.Add(fromNameSubstituteSection);

            sectionSubstitute.GetChildren().Returns(childrenSubstitutes.AsEnumerable());

            // Setup GetChild for each property for the binder
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled)).Returns(enabledSubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.ApiKey)).Returns(apiKeySubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.FromAddress)).Returns(fromAddressSubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)).Returns(fromNameSubstituteSection);

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(sectionSubstitute);

            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute); // Removed .Object
        }

        [Fact]
        public async Task SendEmailAsync_MissingApiKey_ThrowsConfigurationErrorsException()
        {
            // Arrange
            var optionsSectionSubstitute = Substitute.For<IConfigurationSection>();
            optionsSectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Exists().Returns(true);

            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.ApiKey)).Returns(CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey)));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromAddress)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromAddress), "<EMAIL>"));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromName), "Test App"));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.Enabled), "true"));

            var children = new List<IConfigurationSection>
            {
                // Not adding ApiKey to GetChildren() list simulates it missing for some binding scenarios too.
                // However, explicitly setting GetChild("ApiKey").Exists().Returns(false) is more robust.
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromAddress)),
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)),
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled))
                // ApiKey child is effectively missing or set up as non-existent by GetChild().
            };
            optionsSectionSubstitute.GetChildren().Returns(children.AsEnumerable());


            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(optionsSectionSubstitute);
            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ConfigurationErrorsException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message"));
            Assert.Contains("Postmark API key is not configured.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_MissingFromAddress_ThrowsConfigurationErrorsException()
        {
            // Arrange
            var optionsSectionSubstitute = Substitute.For<IConfigurationSection>();
            optionsSectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            optionsSectionSubstitute.Exists().Returns(true);

            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.ApiKey)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.ApiKey), "test_api_key"));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromAddress)).Returns(CreateNonExistentConfigSectionSubstitute(nameof(EmailSenderOptions.FromAddress)));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.FromName), "Test App"));
            optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled)).Returns(CreateConfigSectionSubstitute(nameof(EmailSenderOptions.Enabled), "true"));

            var children = new List<IConfigurationSection>
            {
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.ApiKey)),
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)),
                optionsSectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled))
                // FromAddress child is effectively missing or set up as non-existent by GetChild().
            };
            optionsSectionSubstitute.GetChildren().Returns(children.AsEnumerable());

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(optionsSectionSubstitute);
            _postmarkEmailSenderService = new PostmarkEmailSenderService(_configurationSubstitute, _loggerSubstitute);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ConfigurationErrorsException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message"));
            Assert.Contains("Postmark From Address is not configured.", exception.Message);
        }

        [Fact]
        public async Task SendEmailAsync_PostmarkClientThrowsException_ThrowsEmailExceptionAndLogsError()
        {
            // Arrange
            SetupEmailOptions(enabled: true, apiKey: "bogus_api_key_that_will_fail", fromAddress: "<EMAIL>");
            var expectedLogMessagePart = "Error sending email with Postmark:";

            // Act & Assert
            var exception = await Assert.ThrowsAsync<EmailException>(
                () => _postmarkEmailSenderService.SendEmailAsync("<EMAIL>", "Subject", "Message"));

            _loggerSubstitute.Received(1).Log( // Changed
                LogLevel.Error,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessagePart)),
                Arg.Any<Exception>(),
                Arg.Any<Func<object, Exception?, string>>());
        }
    }
}
