using System;
using System.Collections.Generic;
using System.Linq;
using Xunit;
using ProScoring.Infrastructure.Services;

namespace ProScoring.Tests.Infrastructure.Services
{
    public class GuidishIdGenerationUtilServiceTests
    {
        [Fact]
        public void NewGuidishId_ReturnsCorrectLength()
        {
            // Arrange
            var service = new GuidishIdGenerationUtilService();

            // Act
            var id = service.NewGuidishId();

            // Assert
            Assert.Equal(10, id.Length);
        }

        [Fact]
        public void NewGuidishId_ReturnsUniqueIds()
        {
            // Arrange
            var service = new GuidishIdGenerationUtilService();
            var generatedIds = new HashSet<string>();
            var numberOfIdsToGenerate = 100;

            // Act
            for (int i = 0; i < numberOfIdsToGenerate; i++)
            {
                generatedIds.Add(service.NewGuidishId());
            }

            // Assert
            Assert.Equal(numberOfIdsToGenerate, generatedIds.Count);
        }

        [Fact]
        public void NewGuidishId_StartsWithLetterAndContainsValidChars()
        {
            // Arrange
            var service = new GuidishIdGenerationUtilService();

            // Act
            var id = service.NewGuidishId();

            // Assert
            Assert.True(id.Length > 0, "ID should not be empty."); // Ensure ID is not empty before checking characters
            Assert.True(char.IsLetter(id[0]), $"First character '{id[0]}' should be a letter.");
            Assert.True(id.All(char.IsLetterOrDigit), $"ID '{id}' should only contain letters or digits.");
        }
    }
}
