using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NSubstitute; // Changed from Moq
using Xunit;
using ProScoring.Domain.Entities; // For ApplicationUser
using ProScoring.Infrastructure.Options;
using ProScoring.Infrastructure.Services;
using Microsoft.AspNetCore.Identity.UI.Services; // For IEmailSender
using System.Linq; // For .ToArray() on GetChildren
using System.Collections.Generic; // For List

namespace ProScoring.Tests.Infrastructure.Services
{
    public class EmailSenderServiceTests
    {
        private readonly IEmailSender _actualSenderSubstitute; // Changed
        private readonly IConfiguration _configurationSubstitute; // Changed
        private readonly ILogger<EmailSenderService> _loggerSubstitute; // Changed
        private readonly ApplicationUser _testUser;
        private EmailSenderService _emailSenderService;

        public EmailSenderServiceTests()
        {
            _actualSenderSubstitute = Substitute.For<IEmailSender>();
            _configurationSubstitute = Substitute.For<IConfiguration>();
            _loggerSubstitute = Substitute.For<ILogger<EmailSenderService>>();
            _testUser = new ApplicationUser { Email = "<EMAIL>", UserName = "testuser" };
        }

        private void SetupEmailOptions(bool enabled, string apiKey = "test_api_key", string fromAddress = "<EMAIL>", string fromName = "Test App")
        {
            // As IConfigurationSection.Get<T>() is a static extension method, we mock the underlying structure
            // that it uses (GetChildren, Value, Exists) for EmailSenderService to bind EmailSenderOptions.
            var sectionSubstitute = Substitute.For<IConfigurationSection>();
            sectionSubstitute.Key.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Path.Returns(EmailSenderOptions.SECTION_NAME);
            sectionSubstitute.Exists().Returns(true);
            sectionSubstitute.Value.Returns((string)null); // Parent section usually has no direct value for complex objects

            var childrenSubstitutes = new List<IConfigurationSection>();

            var enabledSubstituteSection = Substitute.For<IConfigurationSection>();
            enabledSubstituteSection.Key.Returns(nameof(EmailSenderOptions.Enabled));
            enabledSubstituteSection.Path.Returns($"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.Enabled)}");
            enabledSubstituteSection.Value.Returns(enabled.ToString().ToLowerInvariant()); // .NET config binder is case-insensitive for bools
            enabledSubstituteSection.Exists().Returns(true);
            childrenSubstitutes.Add(enabledSubstituteSection);

            var apiKeySubstituteSection = Substitute.For<IConfigurationSection>();
            apiKeySubstituteSection.Key.Returns(nameof(EmailSenderOptions.ApiKey));
            apiKeySubstituteSection.Path.Returns($"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.ApiKey)}");
            apiKeySubstituteSection.Value.Returns(apiKey);
            apiKeySubstituteSection.Exists().Returns(true);
            childrenSubstitutes.Add(apiKeySubstituteSection);

            var fromAddressSubstituteSection = Substitute.For<IConfigurationSection>();
            fromAddressSubstituteSection.Key.Returns(nameof(EmailSenderOptions.FromAddress));
            fromAddressSubstituteSection.Path.Returns($"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromAddress)}");
            fromAddressSubstituteSection.Value.Returns(fromAddress);
            fromAddressSubstituteSection.Exists().Returns(true);
            childrenSubstitutes.Add(fromAddressSubstituteSection);

            var fromNameSubstituteSection = Substitute.For<IConfigurationSection>();
            fromNameSubstituteSection.Key.Returns(nameof(EmailSenderOptions.FromName));
            fromNameSubstituteSection.Path.Returns($"{EmailSenderOptions.SECTION_NAME}:{nameof(EmailSenderOptions.FromName)}");
            fromNameSubstituteSection.Value.Returns(fromName);
            fromNameSubstituteSection.Exists().Returns(true);
            childrenSubstitutes.Add(fromNameSubstituteSection);

            sectionSubstitute.GetChildren().Returns(childrenSubstitutes.AsEnumerable());

            // Setup GetChild for each property for the binder
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.Enabled)).Returns(enabledSubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.ApiKey)).Returns(apiKeySubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.FromAddress)).Returns(fromAddressSubstituteSection);
            sectionSubstitute.GetChild(nameof(EmailSenderOptions.FromName)).Returns(fromNameSubstituteSection);

            _configurationSubstitute.GetSection(EmailSenderOptions.SECTION_NAME).Returns(sectionSubstitute);

            _emailSenderService = new EmailSenderService(_actualSenderSubstitute, _configurationSubstitute, _loggerSubstitute); // Removed .Object
        }

        // --- SendConfirmationLinkAsync Tests ---
        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled. Skipping SendConfirmationLinkAsync.";

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            _actualSenderSubstitute.DidNotReceive().SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute.Received(1).Log( // Changed
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString()!.Contains(expectedLogMessage)),
                null,
                Arg.Any<Func<object, Exception?, string>>());
        }

        [Fact]
        public async Task SendConfirmationLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendConfirmationLinkAsync(_testUser, "<EMAIL>", "confirm_link");

            // Assert
            _actualSenderSubstitute.Received(1).SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }

        // --- SendPasswordResetCodeAsync Tests ---
        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled. Skipping SendPasswordResetCodeAsync.";

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            _actualSenderSubstitute.DidNotReceive().SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute.Received(1).Log( // Changed
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString().Contains(expectedLogMessage)),
                null,
                Arg.Any<Func<object, Exception, string>>());
        }

        [Fact]
        public async Task SendPasswordResetCodeAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetCodeAsync(_testUser, "<EMAIL>", "reset_code");

            // Assert
            _actualSenderSubstitute.Received(1).SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }

        // --- SendPasswordResetLinkAsync Tests ---
        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingDisabled_LogsWarningAndDoesNotCallSender()
        {
            // Arrange
            SetupEmailOptions(enabled: false);
            var expectedLogMessage = "Email sending is disabled. Skipping SendPasswordResetLinkAsync.";

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            _actualSenderSubstitute.DidNotReceive().SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
            _loggerSubstitute.Received(1).Log( // Changed
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString().Contains(expectedLogMessage)),
                null,
                Arg.Any<Func<object, Exception, string>>());
        }

        [Fact]
        public async Task SendPasswordResetLinkAsync_WhenEmailSendingEnabled_CallsActualSender()
        {
            // Arrange
            SetupEmailOptions(enabled: true);

            // Act
            await _emailSenderService.SendPasswordResetLinkAsync(_testUser, "<EMAIL>", "reset_link");

            // Assert
            _actualSenderSubstitute.Received(1).SendEmailAsync(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>()); // Changed
        }
    }
}
