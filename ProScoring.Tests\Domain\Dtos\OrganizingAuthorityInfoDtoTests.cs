using System;
using FluentAssertions;
using ProScoring.Domain.Dtos;
using ProScoring.Domain.Entities;
using Xunit;

namespace ProScoring.Tests.Domain.Dtos
{
    public class OrganizingAuthorityInfoDtoTests
    {
        // --- Tests for static FromEntity(OrganizingAuthority entity) method ---

        [Fact]
        public void FromEntity_AllPropertiesSet_MapsCorrectlyToDto()
        {
            // Arrange
            var entity = new OrganizingAuthority
            {
                Id = "oa-info-123",
                Name = "Info Test OA",
                Email = "<EMAIL>",
                Phone = "************",
                Website = "http://info-testoa.com",
                Private = true, // DTO property
                AddressLine1 = "1 Test Address Ln", // Not directly in InfoDto, but good to have in entity
                AddressLine2 = "Apt 1A", // Not directly in InfoDto
                City = "Info City",
                State = "IS",
                PostalCode = "12312", // Not directly in InfoDto
                Country = "ICY",
                Approved = true, // DTO property
                ImageId = "img-info-456",
                CreatedDate = DateTimeOffset.UtcNow.AddDays(-10),
                CreatedById = "user-creator",
                UpdatedDate = DateTimeOffset.UtcNow.AddDays(-1),
                UpdatedById = "user-updater"
            };

            // Act
            var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

            // Assert
            dto.Should().NotBeNull();
            dto.Id.Should().Be(entity.Id);
            dto.Name.Should().Be(entity.Name);
            dto.Email.Should().Be(entity.Email);
            dto.Phone.Should().Be(entity.Phone);
            dto.Website.Should().Be(entity.Website);
            dto.City.Should().Be(entity.City);
            dto.State.Should().Be(entity.State);
            dto.Country.Should().Be(entity.Country);
            dto.ImageId.Should().Be(entity.ImageId);
            dto.Private.Should().Be(entity.Private);
            dto.Approved.Should().Be(entity.Approved);
            // Verify properties NOT in InfoDto are not present (implicitly by not checking them)
        }

        [Fact]
        public void FromEntity_SomePropertiesNull_MapsNullsCorrectlyToDto()
        {
            // Arrange
            var entity = new OrganizingAuthority
            {
                Id = "oa-info-789",
                Name = "Info OA With Nulls",
                Email = null, // Nullable
                Phone = null, // Nullable
                Website = null, // Nullable
                Private = false,
                AddressLine1 = "789 Null St", // Not in DTO
                City = null, // Nullable
                State = null, // Nullable
                Country = null, // Nullable
                Approved = false,
                ImageId = null, // Nullable
                CreatedDate = DateTimeOffset.UtcNow,
                CreatedById = "user-another"
            };

            // Act
            var dto = OrganizingAuthorityInfoDto.FromEntity(entity);

            // Assert
            dto.Should().NotBeNull();
            dto.Id.Should().Be(entity.Id);
            dto.Name.Should().Be(entity.Name);
            dto.Email.Should().BeNull();
            dto.Phone.Should().BeNull();
            dto.Website.Should().BeNull();
            dto.City.Should().BeNull();
            dto.State.Should().BeNull();
            dto.Country.Should().BeNull();
            dto.ImageId.Should().BeNull();
            dto.Private.Should().Be(entity.Private);
            dto.Approved.Should().Be(entity.Approved);
        }
    }
}
