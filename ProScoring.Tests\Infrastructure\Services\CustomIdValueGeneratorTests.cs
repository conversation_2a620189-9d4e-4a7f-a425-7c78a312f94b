using System;
using Xunit;
using NSubstitute; // Changed from Moq
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using ProScoring.Domain.Entities.EntityInterfaces;
using ProScoring.Infrastructure.ServiceInterfaces;
using ProScoring.Infrastructure.Services;
using System.Collections.Generic; // For IReadOnlyList if used in logger, and for general collections

namespace ProScoring.Tests.Infrastructure.Services
{
    // Stub class for testing
    public class StubHasAutoInsertedId : IHasAutoInsertedId
    {
        public string Id { get; set; }
    }

    public class CustomIdValueGeneratorTests
    {
        private readonly IIdGenerationUtilService _mockIdUtilsService; // Changed from Mock<T>
        private readonly ILogger<CustomIdValueGenerator> _mockLogger; // Changed from Mock<T>
        private readonly CustomIdValueGenerator _generator;

        public CustomIdValueGeneratorTests()
        {
            _mockIdUtilsService = Substitute.For<IIdGenerationUtilService>();
            _mockLogger = Substitute.For<ILogger<CustomIdValueGenerator>>();
            _generator = new CustomIdValueGenerator(_mockIdUtilsService, _mockLogger); // Removed .Object
        }

        // Helper to create a mock EntityEntry
        private EntityEntry CreateMockEntityEntry(object entity) // Return type changed
        {
            // EntityEntry can be tricky to substitute directly if it has many non-virtual members
            // or internal constructors that NSubstitute cannot easily handle.
            // However, if we only need to mock its 'Entity' property (which is virtual),
            // Substitute.For<EntityEntry>() should work.
            var entry = Substitute.For<EntityEntry>();
            entry.Entity.Returns(entity); // Setup the 'Entity' property directly
            return entry; // Return the substitute instance
        }


        [Fact]
        public void Next_WhenEntityImplementsIHasAutoInsertedId_CallsIdUtilsService()
        {
            // Arrange
            var stubEntity = new StubHasAutoInsertedId();
            var mockEntityEntry = CreateMockEntityEntry(stubEntity);

            var expectedId = "test-id";
            _mockIdUtilsService.GenerateId(stubEntity).Returns(expectedId); // Changed setup

            // Act
            var actualId = _generator.Next(mockEntityEntry); // Removed .Object

            // Assert
            _mockIdUtilsService.Received(1).GenerateId(Arg.Is<IHasAutoInsertedId>(e => e == stubEntity)); // Changed verification
            Assert.Equal(expectedId, actualId);
        }

        [Fact]
        public void Next_WhenEntityDoesNotImplementIHasAutoInsertedId_ReturnsGuidAndLogsWarning()
        {
            // Arrange
            var plainObject = new object();
            var mockEntityEntry = CreateMockEntityEntry(plainObject);
            var expectedLogMessagePart = $"Entity of type '{plainObject.GetType().FullName}' does not implement IHasAutoInsertedId. Falling back to Guid.NewGuid().";
            
            // Act
            var actualId = _generator.Next(mockEntityEntry); // Removed .Object

            // Assert
            _mockIdUtilsService.DidNotReceive().GenerateId(Arg.Any<IHasAutoInsertedId>()); // Changed verification
            Assert.NotNull(actualId); 
            Assert.True(Guid.TryParse(actualId.ToString(), out _), $"Generated ID '{actualId}' was not a valid GUID.");

            _mockLogger.Received(1).Log(
                LogLevel.Warning,
                Arg.Any<EventId>(),
                Arg.Is<object>(state => state.ToString().Contains(expectedLogMessagePart)), // More direct state check
                null, // No exception expected
                Arg.Any<Func<object, Exception, string>>()); // Formatter
        }

        [Fact]
        public void GeneratesTemporaryValues_IsFalse()
        {
            // Arrange
            // No specific arrangement needed beyond constructor setup

            // Act
            var generatesTemporaryValues = _generator.GeneratesTemporaryValues;

            // Assert
            Assert.False(generatesTemporaryValues);
        }
    }
}
